{"name": "wp-plugin-tracker-backend", "version": "1.0.0", "description": "Backend API for WordPress Plugin Tracker - Optimized Version", "main": "server.js", "type": "module", "directories": {"test": "tests"}, "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node src/scripts/seedUsers.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["wordpress", "plugin", "tracker", "api", "express", "mongodb"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.9.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.2", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.1.10"}}