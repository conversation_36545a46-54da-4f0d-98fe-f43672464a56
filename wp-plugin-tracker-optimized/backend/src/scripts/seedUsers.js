import mongoose from 'mongoose';
import dotenv from 'dotenv';
import User from '../models/User.js';

// Load environment variables
dotenv.config();

const seedUsers = async () => {
  try {
    // Connect to MongoDB
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/wp-plugin-tracker-optimized';
    await mongoose.connect(mongoURI);
    console.log('✅ Connected to MongoDB');

    // Check if superadmin already exists
    const existingSuperAdmin = await User.findOne({ role: 'superadmin' });
    
    if (existingSuperAdmin) {
      console.log('⚠️  Superadmin user already exists');
      console.log(`Username: ${existingSuperAdmin.username}`);
      console.log(`Email: ${existingSuperAdmin.email}`);
      process.exit(0);
    }

    // Create default superadmin user
    const superAdminData = {
      name: 'Super Administrator',
      username: 'superadmin',
      email: '<EMAIL>',
      password: 'admin123456',
      role: 'superadmin',
      isActive: true
    };

    const superAdmin = new User(superAdminData);
    await superAdmin.save();

    console.log('✅ Default users created successfully!');
    console.log('\n📋 Login Credentials:');
    console.log('='.repeat(30));
    console.log(`Username: ${superAdmin.username}`);
    console.log(`Password: admin123456`);
    console.log(`Email: ${superAdmin.email}`);
    console.log(`Role: ${superAdmin.role}`);
    console.log('='.repeat(30));
    console.log('\n⚠️  Please change the default password after first login!');

  } catch (error) {
    console.error('❌ Error seeding users:', error.message);
    process.exit(1);
  } finally {
    mongoose.connection.close();
    console.log('📦 Database connection closed');
    process.exit(0);
  }
};

// Run the seed function
seedUsers();
