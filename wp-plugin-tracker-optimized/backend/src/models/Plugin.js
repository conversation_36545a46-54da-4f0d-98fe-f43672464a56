import mongoose from 'mongoose';

const pluginSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Plugin name is required'],
    trim: true
  },
  slug: {
    type: String,
    required: [true, 'Plugin slug is required'],
    unique: true,
    trim: true,
    lowercase: true
  },
  description: {
    type: String,
    default: ''
  },
  version: {
    type: String,
    default: '1.0.0'
  },
  author: {
    type: String,
    default: ''
  },
  authorProfile: {
    type: String,
    default: ''
  },
  homepage: {
    type: String,
    default: ''
  },
  downloadLink: {
    type: String,
    default: ''
  },
  tags: [{
    type: String,
    trim: true
  }],
  rating: {
    type: Number,
    default: 0,
    min: 0,
    max: 5
  },
  numRatings: {
    type: Number,
    default: 0
  },
  downloaded: {
    type: Number,
    default: 0
  },
  activeInstalls: {
    type: Number,
    default: 0
  },
  lastUpdated: {
    type: Date,
    default: Date.now
  },
  added: {
    type: Date,
    default: Date.now
  },
  compatibility: {
    type: Map,
    of: String,
    default: new Map()
  },
  screenshots: [{
    src: String,
    caption: String
  }],
  banners: {
    low: String,
    high: String
  },
  icons: {
    '1x': String,
    '2x': String,
    svg: String
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'closed'],
    default: 'active'
  },
  isTracking: {
    type: Boolean,
    default: true
  },
  lastFetched: {
    type: Date,
    default: Date.now
  },
  fetchError: {
    type: String,
    default: null
  },
  addedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
});

// Indexes for better query performance
pluginSchema.index({ slug: 1 });
pluginSchema.index({ status: 1 });
pluginSchema.index({ isTracking: 1 });
pluginSchema.index({ lastFetched: 1 });
pluginSchema.index({ downloaded: -1 });
pluginSchema.index({ rating: -1 });

// Virtual for WordPress.org URL
pluginSchema.virtual('wordpressUrl').get(function() {
  return `https://wordpress.org/plugins/${this.slug}/`;
});

// Virtual for download count formatted
pluginSchema.virtual('downloadFormatted').get(function() {
  return this.downloaded.toLocaleString();
});

// Method to update plugin data from WordPress.org API
pluginSchema.methods.updateFromWordPressAPI = function(apiData) {
  this.name = apiData.name || this.name;
  this.description = apiData.short_description || this.description;
  this.version = apiData.version || this.version;
  this.author = apiData.author || this.author;
  this.authorProfile = apiData.author_profile || this.authorProfile;
  this.homepage = apiData.homepage || this.homepage;
  this.downloadLink = apiData.download_link || this.downloadLink;
  this.tags = apiData.tags || this.tags;
  this.rating = apiData.rating || this.rating;
  this.numRatings = apiData.num_ratings || this.numRatings;
  this.downloaded = apiData.downloaded || this.downloaded;
  this.activeInstalls = apiData.active_installs || this.activeInstalls;
  this.lastUpdated = apiData.last_updated ? new Date(apiData.last_updated) : this.lastUpdated;
  this.added = apiData.added ? new Date(apiData.added) : this.added;
  this.screenshots = apiData.screenshots || this.screenshots;
  this.banners = apiData.banners || this.banners;
  this.icons = apiData.icons || this.icons;
  this.lastFetched = new Date();
  this.fetchError = null;
  
  return this;
};

// Method to mark fetch error
pluginSchema.methods.markFetchError = function(error) {
  this.fetchError = error;
  this.lastFetched = new Date();
  return this;
};

// Static method to get active plugins
pluginSchema.statics.getActivePlugins = function() {
  return this.find({ status: 'active', isTracking: true });
};

// Static method to get plugins needing update
pluginSchema.statics.getPluginsNeedingUpdate = function(hoursAgo = 24) {
  const cutoff = new Date(Date.now() - hoursAgo * 60 * 60 * 1000);
  return this.find({
    status: 'active',
    isTracking: true,
    lastFetched: { $lt: cutoff }
  });
};

const Plugin = mongoose.model('Plugin', pluginSchema);

export default Plugin;
