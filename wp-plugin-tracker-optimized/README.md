# WordPress Plugin Tracker - Optimized Version

An optimized WordPress plugin tracking system with enhanced performance, better architecture, and improved user experience.

## Project Structure

```
wp-plugin-tracker-optimized/
├── frontend/                 # React frontend application
├── backend/                  # Node.js/Express backend API
├── docs/                     # Project documentation
└── README.md                # This file
```

## Features

### Core Functionality
- **Plugin Performance Dashboard**: Track plugin rankings, downloads, and ratings
- **Download Analytics**: Day-wise download tracking with comparative analysis
- **Keyword Analytics**: Monitor plugin rankings for specific keywords
- **User Management**: Role-based access control (Superadmin, Admin, Agent)
- **Daily Work Management**: Task tracking and quick notes

### Key Optimizations
- **Centralized Data Management**: Single data fetch with database storage
- **Efficient API Integration**: WordPress.org API with smart caching
- **Optimized UI/UX**: Space-efficient layouts with improved visual design
- **Performance Monitoring**: Real-time plugin performance tracking

## Technology Stack

### Frontend
- **React** with JSX format
- **React Router** for navigation
- **TailwindCSS** for styling
- **Chart.js/Recharts** for analytics visualization

### Backend
- **Node.js** with Express.js
- **MongoDB** for data storage
- **JWT** for authentication
- **Axios** for API requests

### External APIs
- WordPress.org Plugin API
- WordPress.org Download Statistics API

## Plugins Tracked
- EmbedPress
- SchedulePress
- NotificationX

## Getting Started

### Prerequisites
- Node.js (v16 or higher)
- MongoDB
- npm or yarn

### Installation

1. Clone the repository
2. Install frontend dependencies:
   ```bash
   cd frontend
   npm install
   ```

3. Install backend dependencies:
   ```bash
   cd backend
   npm install
   ```

4. Set up environment variables
5. Start the development servers

## Development Guidelines

- Use package managers for dependency management
- Follow React best practices with functional components
- Implement proper error handling and loading states
- Use TypeScript for better code quality
- Write comprehensive tests

## Currency & Localization
- Primary currency: Bangladeshi Taka (BDT)
- Date format: dd-mm-yyyy (28-05-2025)

## License
MIT License
