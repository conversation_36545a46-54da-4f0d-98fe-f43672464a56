@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-gray-200;
  }
  body {
    @apply bg-gray-50 font-sans antialiased;
    margin: 0;
    padding: 0;
    line-height: 1.6;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold text-gray-900;
  }

  p {
    @apply text-gray-600;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 focus:ring-4 focus:ring-primary-200 text-white font-medium py-2.5 px-5 rounded-xl transition-all duration-200 shadow-sm hover:shadow-md;
  }

  .btn-secondary {
    @apply bg-white hover:bg-gray-50 border border-gray-300 text-gray-700 font-medium py-2.5 px-5 rounded-xl transition-all duration-200 shadow-sm hover:shadow-md;
  }

  .btn-danger {
    @apply bg-danger-500 hover:bg-danger-600 focus:ring-4 focus:ring-danger-200 text-white font-medium py-2.5 px-5 rounded-xl transition-all duration-200 shadow-sm hover:shadow-md;
  }

  .sidebar-item {
    @apply flex items-center px-4 py-3 mx-2 text-gray-300 hover:bg-sidebar-hover hover:text-white transition-all duration-200 cursor-pointer rounded-xl;
  }

  .sidebar-item.active {
    @apply bg-sidebar-active text-white shadow-lg;
  }

  .form-input {
    @apply block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200;
  }

  .card {
    @apply bg-white rounded-2xl shadow-card border border-gray-100 overflow-hidden;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-100 bg-gray-50/50;
  }

  .card-body {
    @apply p-6;
  }

  .stat-card {
    @apply bg-white rounded-2xl shadow-card p-6 border border-gray-100 hover:shadow-soft transition-all duration-200;
  }

  .modal-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 backdrop-blur-sm;
  }

  .modal-content {
    @apply bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden;
  }

  .table-container {
    @apply bg-white rounded-2xl shadow-card border border-gray-100 overflow-hidden;
  }

  .table-header {
    @apply bg-gray-50/50 px-6 py-4 border-b border-gray-100;
  }

  .badge-success {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success-50 text-success-600;
  }

  .badge-warning {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-warning-50 text-warning-600;
  }

  .badge-danger {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-danger-50 text-danger-600;
  }
}
