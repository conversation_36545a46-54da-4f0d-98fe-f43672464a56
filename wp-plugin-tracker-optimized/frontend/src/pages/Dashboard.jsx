import { useState, useEffect } from "react";
import {
  FiRefreshCw,
  FiTrendingUp,
  FiDownload,
  FiStar,
  FiPackage,
  FiSearch,
} from "react-icons/fi";

const Dashboard = () => {
  const [stats, setStats] = useState({
    totalPlugins: 3,
    totalDownloads: 0,
    averageRating: 0,
    activePlugins: 3,
  });
  const [loading, setLoading] = useState(false);

  const plugins = [
    { name: "EmbedPress", downloads: 0, rating: 0, rank: 0 },
    { name: "SchedulePress", downloads: 0, rating: 0, rank: 0 },
    { name: "NotificationX", downloads: 0, rating: 0, rank: 0 },
  ];

  const refreshData = async () => {
    setLoading(true);
    // TODO: Implement API call to refresh data
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-1">
            WordPress Plugin Performance Overview
          </p>
        </div>
        <button
          onClick={refreshData}
          disabled={loading}
          className="btn-primary flex items-center space-x-2 self-start sm:self-auto"
        >
          <FiRefreshCw className={`h-4 w-4 ${loading ? "animate-spin" : ""}`} />
          <span>Refresh Data</span>
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="stat-card">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="p-3 bg-blue-100 rounded-xl">
                <FiPackage className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Plugins</p>
              <p className="text-2xl font-bold text-gray-900">
                {stats.totalPlugins}
              </p>
            </div>
          </div>
        </div>

        <div className="stat-card">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="p-3 bg-green-100 rounded-xl">
                <FiDownload className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">
                Total Downloads
              </p>
              <p className="text-2xl font-bold text-gray-900">
                {stats.totalDownloads.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className="stat-card">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="p-3 bg-yellow-100 rounded-xl">
                <FiStar className="h-6 w-6 text-yellow-600" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">
                Average Rating
              </p>
              <p className="text-2xl font-bold text-gray-900">
                {stats.averageRating.toFixed(1)}
              </p>
            </div>
          </div>
        </div>

        <div className="stat-card">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="p-3 bg-purple-100 rounded-xl">
                <FiTrendingUp className="h-6 w-6 text-purple-600" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">
                Active Plugins
              </p>
              <p className="text-2xl font-bold text-gray-900">
                {stats.activePlugins}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Plugin Performance Overview */}
      <div className="table-container">
        <div className="table-header">
          <h2 className="text-lg font-semibold text-gray-900">
            Plugin Performance Overview
          </h2>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50/50">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Plugin Name
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Downloads
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Rating
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Rank
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-100">
              {plugins.map((plugin, index) => (
                <tr
                  key={index}
                  className="hover:bg-gray-50/50 transition-colors duration-150"
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-semibold text-gray-900">
                      {plugin.name}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 font-medium">
                      {plugin.downloads.toLocaleString()}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <FiStar className="h-4 w-4 text-yellow-500 mr-1" />
                      <span className="text-sm text-gray-900 font-medium">
                        {plugin.rating.toFixed(1)}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 font-medium">
                      #{plugin.rank || "N/A"}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="badge-success">Active</span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="card">
        <div className="card-header">
          <h2 className="text-lg font-semibold text-gray-900">Quick Actions</h2>
        </div>
        <div className="card-body">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <button className="group p-6 border border-gray-200 rounded-xl hover:border-blue-300 hover:shadow-md transition-all duration-200 text-left">
              <div className="p-3 bg-blue-100 rounded-xl w-fit mb-4 group-hover:bg-blue-200 transition-colors duration-200">
                <FiDownload className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">
                Update Download Data
              </h3>
              <p className="text-sm text-gray-600">
                Fetch latest download statistics from WordPress.org
              </p>
            </button>

            <button className="group p-6 border border-gray-200 rounded-xl hover:border-green-300 hover:shadow-md transition-all duration-200 text-left">
              <div className="p-3 bg-green-100 rounded-xl w-fit mb-4 group-hover:bg-green-200 transition-colors duration-200">
                <FiTrendingUp className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">
                Generate Report
              </h3>
              <p className="text-sm text-gray-600">
                Create comprehensive performance report
              </p>
            </button>

            <button className="group p-6 border border-gray-200 rounded-xl hover:border-purple-300 hover:shadow-md transition-all duration-200 text-left">
              <div className="p-3 bg-purple-100 rounded-xl w-fit mb-4 group-hover:bg-purple-200 transition-colors duration-200">
                <FiSearch className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">
                Keyword Analysis
              </h3>
              <p className="text-sm text-gray-600">
                Analyze keyword rankings and trends
              </p>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
