import { NavLink } from "react-router-dom";
import { useAuth } from "../../contexts/AuthContext";
import {
  FiHome,
  FiPackage,
  FiDownload,
  FiBarChart2,
  FiSearch,
  FiCheckSquare,
  FiUsers,
  FiUser,
  FiSettings,
  FiLogOut,
} from "react-icons/fi";

const Sidebar = ({ isOpen }) => {
  const { logout, user } = useAuth();

  const menuItems = [
    { path: "/dashboard", icon: FiHome, label: "Dashboard" },
    { path: "/plugins", icon: FiPackage, label: "Plugins" },
    { path: "/download-tracker", icon: FiDownload, label: "Download Tracker" },
    { path: "/analytics", icon: FiBarChart2, label: "Analytics" },
    { path: "/keyword-analytics", icon: FiSearch, label: "Keyword Analytics" },
    { path: "/daily-work", icon: FiCheckSquare, label: "Daily Work Tasks" },
    { path: "/added-users", icon: FiUsers, label: "Added Users" },
  ];

  const bottomItems = [
    { path: "/profile", icon: FiUser, label: "Profile" },
    { path: "/settings", icon: FiSettings, label: "Settings" },
  ];

  const handleLogout = () => {
    logout();
  };

  return (
    <div
      className={`fixed left-0 top-0 h-full bg-sidebar-bg text-white transition-all duration-300 z-40 ${
        isOpen ? "w-64" : "w-16"
      }`}
    >
      <div className="flex flex-col h-full justify-between">
        {/* Logo and Main Menu */}
        <div>
          {/* Logo */}
          <div className="flex items-center justify-center h-16 border-b border-gray-700">
            {isOpen ? (
              <h1 className="text-xl font-bold">WP Tracker</h1>
            ) : (
              <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold">W</span>
              </div>
            )}
          </div>

          {/* Navigation Menu */}
          <nav className="mt-8">
            <div className="px-4 space-y-2">
              {menuItems.map((item) => (
                <NavLink
                  key={item.path}
                  to={item.path}
                  className={({ isActive }) =>
                    `sidebar-item ${isActive ? "active" : ""} ${
                      !isOpen ? "justify-center px-2" : ""
                    }`
                  }
                  title={!isOpen ? item.label : ""}
                >
                  <item.icon className={`h-5 w-5 ${isOpen ? "mr-3" : ""}`} />
                  {isOpen && <span>{item.label}</span>}
                </NavLink>
              ))}
            </div>
          </nav>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-700" style={{ height: "80px" }}>
          <div className="px-4 py-4 space-y-2">
            {bottomItems.map((item) => (
              <NavLink
                key={item.path}
                to={item.path}
                className={({ isActive }) =>
                  `sidebar-item ${isActive ? "active" : ""} ${
                    !isOpen ? "justify-center px-2" : ""
                  }`
                }
                title={!isOpen ? item.label : ""}
              >
                <item.icon className={`h-5 w-5 ${isOpen ? "mr-3" : ""}`} />
                {isOpen && <span>{item.label}</span>}
              </NavLink>
            ))}

            <button
              onClick={handleLogout}
              className={`sidebar-item w-full text-left hover:bg-red-600 ${
                !isOpen ? "justify-center px-2" : ""
              }`}
              title={!isOpen ? "Logout" : ""}
            >
              <FiLogOut className={`h-5 w-5 ${isOpen ? "mr-3" : ""}`} />
              {isOpen && <span>Logout</span>}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
