import { NavLink } from "react-router-dom";
import { useAuth } from "../../contexts/AuthContext";
import {
  FiHome,
  FiPackage,
  FiDownload,
  FiBarChart2,
  FiSearch,
  FiCheckSquare,
  FiUsers,
  FiUser,
  FiSettings,
  FiLogOut,
} from "react-icons/fi";

const Sidebar = ({ isOpen }) => {
  const { logout, user } = useAuth();

  const menuItems = [
    { path: "/dashboard", icon: FiHome, label: "Dashboard" },
    { path: "/plugins", icon: FiPackage, label: "Plugins" },
    { path: "/download-tracker", icon: FiDownload, label: "Download Tracker" },
    { path: "/analytics", icon: FiBarChart2, label: "Analytics" },
    { path: "/keyword-analytics", icon: FiSearch, label: "Keyword Analytics" },
    { path: "/daily-work", icon: FiCheckSquare, label: "Daily Work Tasks" },
    { path: "/added-users", icon: FiUsers, label: "Added Users" },
  ];

  const bottomItems = [
    { path: "/profile", icon: FiUser, label: "Profile" },
    { path: "/settings", icon: FiSettings, label: "Settings" },
  ];

  const handleLogout = () => {
    logout();
  };

  return (
    <div
      className={`fixed left-0 top-0 h-full bg-sidebar-bg text-white transition-all duration-300 ease-in-out z-40 shadow-2xl ${
        isOpen ? "w-64" : "w-16"
      }`}
    >
      <div className="flex flex-col h-full justify-between">
        {/* Logo and Main Menu */}
        <div className="flex-1">
          {/* Logo */}
          <div className="flex items-center justify-center h-16 border-b border-gray-700/50">
            {isOpen ? (
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center shadow-lg">
                  <span className="text-white font-bold text-sm">WP</span>
                </div>
                <h1 className="text-xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                  Plugin Tracker
                </h1>
              </div>
            ) : (
              <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-sm">WP</span>
              </div>
            )}
          </div>

          {/* Navigation Menu */}
          <nav className="mt-6">
            <div className="px-3 space-y-1">
              {menuItems.map((item) => (
                <NavLink
                  key={item.path}
                  to={item.path}
                  className={({ isActive }) =>
                    `sidebar-item ${isActive ? "active" : ""} ${
                      !isOpen ? "justify-center px-2" : ""
                    }`
                  }
                  title={!isOpen ? item.label : ""}
                >
                  <item.icon
                    className={`h-5 w-5 flex-shrink-0 ${isOpen ? "mr-3" : ""}`}
                  />
                  {isOpen && <span className="font-medium">{item.label}</span>}
                </NavLink>
              ))}
            </div>
          </nav>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-700/50 bg-gray-900/30">
          <div className="px-3 py-4 space-y-1">
            {bottomItems.map((item) => (
              <NavLink
                key={item.path}
                to={item.path}
                className={({ isActive }) =>
                  `sidebar-item ${isActive ? "active" : ""} ${
                    !isOpen ? "justify-center px-2" : ""
                  }`
                }
                title={!isOpen ? item.label : ""}
              >
                <item.icon
                  className={`h-5 w-5 flex-shrink-0 ${isOpen ? "mr-3" : ""}`}
                />
                {isOpen && <span className="font-medium">{item.label}</span>}
              </NavLink>
            ))}

            <button
              onClick={handleLogout}
              className={`sidebar-item w-full text-left hover:bg-danger-600 hover:text-white ${
                !isOpen ? "justify-center px-2" : ""
              }`}
              title={!isOpen ? "Logout" : ""}
            >
              <FiLogOut
                className={`h-5 w-5 flex-shrink-0 ${isOpen ? "mr-3" : ""}`}
              />
              {isOpen && <span className="font-medium">Logout</span>}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
