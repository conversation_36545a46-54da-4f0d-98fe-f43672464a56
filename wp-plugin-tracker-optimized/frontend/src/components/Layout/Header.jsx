import { useAuth } from "../../contexts/AuthContext";
import { FiMenu, FiBell, FiUser } from "react-icons/fi";

const Header = ({ toggleSidebar, sidebarOpen }) => {
  const { user } = useAuth();

  return (
    <header className="bg-white shadow-soft border-b border-gray-200/60 backdrop-blur-sm">
      <div className="flex items-center justify-between px-6 py-4">
        {/* Left side - Toggle button */}
        <div className="flex items-center">
          <button
            onClick={toggleSidebar}
            className="p-2.5 rounded-xl text-gray-600 hover:bg-gray-100 hover:text-gray-900 transition-all duration-200 shadow-sm"
            title="Toggle Sidebar"
          >
            <FiMenu className="h-5 w-5" />
          </button>
        </div>

        {/* Right side - User info and notifications */}
        <div className="flex items-center space-x-4">
          {/* Notifications */}
          <button className="p-2.5 rounded-xl text-gray-600 hover:bg-gray-100 hover:text-gray-900 transition-all duration-200 relative shadow-sm">
            <FiBell className="h-5 w-5" />
            <span className="absolute top-1.5 right-1.5 h-2 w-2 bg-danger-500 rounded-full animate-pulse"></span>
          </button>

          {/* User Profile */}
          <div className="flex items-center space-x-3 bg-gray-50 rounded-xl px-3 py-2">
            <div className="flex items-center justify-center h-9 w-9 rounded-xl bg-gradient-to-br from-primary-500 to-primary-600 shadow-lg">
              <FiUser className="h-4 w-4 text-white" />
            </div>
            <div className="hidden md:block">
              <p className="text-sm font-semibold text-gray-900">
                {user?.name || user?.username || "User"}
              </p>
              <p className="text-xs text-gray-500 capitalize font-medium">
                {user?.role || "Admin"}
              </p>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
