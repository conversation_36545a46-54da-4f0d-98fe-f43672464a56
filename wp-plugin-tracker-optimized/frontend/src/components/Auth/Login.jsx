import { useState } from "react";
import { Navigate } from "react-router-dom";
import { useAuth } from "../../contexts/AuthContext";
import { FiU<PERSON>, <PERSON>L<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>yeOff, FiShield } from "react-icons/fi";

const Login = () => {
  const [formData, setFormData] = useState({
    username: "",
    password: "",
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { login, isAuthenticated } = useAuth();

  // Redirect if already authenticated
  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    const result = await login(formData);

    setIsLoading(false);

    if (!result.success) {
      // Error handling is done in the AuthContext
    }
  };

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
        padding: "3rem 1rem",
        fontFamily: "Inter, system-ui, sans-serif",
      }}
    >
      <div style={{ maxWidth: "28rem", width: "100%" }}>
        {/* Header Section */}
        <div style={{ textAlign: "center", marginBottom: "2rem" }}>
          <div
            style={{
              width: "4rem",
              height: "4rem",
              margin: "0 auto",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              borderRadius: "1rem",
              background: "linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%)",
              boxShadow: "0 10px 25px -5px rgba(14, 165, 233, 0.3)",
              marginBottom: "1.5rem",
            }}
          >
            <FiShield style={{ color: "white", fontSize: "1.5rem" }} />
          </div>
          <h1
            style={{
              fontSize: "2rem",
              fontWeight: "700",
              color: "#0f172a",
              marginBottom: "0.5rem",
              lineHeight: "1.2",
            }}
          >
            WordPress Plugin Tracker
          </h1>
          <p style={{ color: "#64748b", fontSize: "0.95rem" }}>
            Professional plugin performance monitoring
          </p>
        </div>

        {/* Login Form */}
        <div
          style={{
            background: "white",
            borderRadius: "1.5rem",
            padding: "2.5rem",
            boxShadow:
              "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
            border: "1px solid #f1f5f9",
          }}
        >
          <form onSubmit={handleSubmit}>
            {/* Username Field */}
            <div style={{ marginBottom: "1.5rem" }}>
              <label
                htmlFor="username"
                style={{
                  display: "block",
                  fontSize: "0.875rem",
                  fontWeight: "600",
                  color: "#374151",
                  marginBottom: "0.5rem",
                }}
              >
                Username
              </label>
              <div style={{ position: "relative" }}>
                <div
                  style={{
                    position: "absolute",
                    left: "1rem",
                    top: "50%",
                    transform: "translateY(-50%)",
                    pointerEvents: "none",
                  }}
                >
                  <FiUser style={{ color: "#9ca3af", fontSize: "1.25rem" }} />
                </div>
                <input
                  id="username"
                  name="username"
                  type="text"
                  required
                  value={formData.username}
                  onChange={handleChange}
                  placeholder="Enter your username"
                  style={{
                    width: "100%",
                    padding: "0.875rem 1rem 0.875rem 3rem",
                    border: "2px solid #e5e7eb",
                    borderRadius: "0.75rem",
                    fontSize: "1rem",
                    transition: "all 0.2s",
                    outline: "none",
                    backgroundColor: "#fafafa",
                  }}
                  onFocus={(e) => {
                    e.target.style.borderColor = "#0ea5e9";
                    e.target.style.backgroundColor = "white";
                    e.target.style.boxShadow =
                      "0 0 0 3px rgba(14, 165, 233, 0.1)";
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = "#e5e7eb";
                    e.target.style.backgroundColor = "#fafafa";
                    e.target.style.boxShadow = "none";
                  }}
                />
              </div>
            </div>

            {/* Password Field */}
            <div style={{ marginBottom: "2rem" }}>
              <label
                htmlFor="password"
                style={{
                  display: "block",
                  fontSize: "0.875rem",
                  fontWeight: "600",
                  color: "#374151",
                  marginBottom: "0.5rem",
                }}
              >
                Password
              </label>
              <div style={{ position: "relative" }}>
                <div
                  style={{
                    position: "absolute",
                    left: "1rem",
                    top: "50%",
                    transform: "translateY(-50%)",
                    pointerEvents: "none",
                  }}
                >
                  <FiLock style={{ color: "#9ca3af", fontSize: "1.25rem" }} />
                </div>
                <input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  required
                  value={formData.password}
                  onChange={handleChange}
                  placeholder="Enter your password"
                  style={{
                    width: "100%",
                    padding: "0.875rem 3rem 0.875rem 3rem",
                    border: "2px solid #e5e7eb",
                    borderRadius: "0.75rem",
                    fontSize: "1rem",
                    transition: "all 0.2s",
                    outline: "none",
                    backgroundColor: "#fafafa",
                  }}
                  onFocus={(e) => {
                    e.target.style.borderColor = "#0ea5e9";
                    e.target.style.backgroundColor = "white";
                    e.target.style.boxShadow =
                      "0 0 0 3px rgba(14, 165, 233, 0.1)";
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = "#e5e7eb";
                    e.target.style.backgroundColor = "#fafafa";
                    e.target.style.boxShadow = "none";
                  }}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  style={{
                    position: "absolute",
                    right: "1rem",
                    top: "50%",
                    transform: "translateY(-50%)",
                    background: "none",
                    border: "none",
                    cursor: "pointer",
                    color: "#9ca3af",
                    fontSize: "1.25rem",
                    transition: "color 0.2s",
                  }}
                  onMouseEnter={(e) => (e.target.style.color = "#6b7280")}
                  onMouseLeave={(e) => (e.target.style.color = "#9ca3af")}
                >
                  {showPassword ? <FiEyeOff /> : <FiEye />}
                </button>
              </div>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading}
              style={{
                width: "100%",
                padding: "0.875rem 1.5rem",
                background: isLoading
                  ? "#94a3b8"
                  : "linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%)",
                color: "white",
                border: "none",
                borderRadius: "0.75rem",
                fontSize: "1rem",
                fontWeight: "600",
                cursor: isLoading ? "not-allowed" : "pointer",
                transition: "all 0.2s",
                boxShadow: "0 4px 14px 0 rgba(14, 165, 233, 0.3)",
                marginBottom: "1.5rem",
              }}
              onMouseEnter={(e) => {
                if (!isLoading) {
                  e.target.style.transform = "translateY(-1px)";
                  e.target.style.boxShadow =
                    "0 6px 20px 0 rgba(14, 165, 233, 0.4)";
                }
              }}
              onMouseLeave={(e) => {
                if (!isLoading) {
                  e.target.style.transform = "translateY(0)";
                  e.target.style.boxShadow =
                    "0 4px 14px 0 rgba(14, 165, 233, 0.3)";
                }
              }}
            >
              {isLoading ? (
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <div
                    style={{
                      width: "1.25rem",
                      height: "1.25rem",
                      border: "2px solid transparent",
                      borderTop: "2px solid white",
                      borderRadius: "50%",
                      animation: "spin 1s linear infinite",
                      marginRight: "0.5rem",
                    }}
                  />
                  Signing in...
                </div>
              ) : (
                "Sign in to Dashboard"
              )}
            </button>

            {/* Credentials Info */}
            <div
              style={{
                background: "linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)",
                borderRadius: "0.75rem",
                padding: "1rem",
                border: "1px solid #e2e8f0",
              }}
            >
              <div style={{ textAlign: "center" }}>
                <p
                  style={{
                    fontSize: "0.875rem",
                    color: "#64748b",
                    marginBottom: "0.75rem",
                    fontWeight: "600",
                  }}
                >
                  🔑 Default Login Credentials
                </p>
                <div
                  style={{
                    display: "grid",
                    gridTemplateColumns: "1fr 1fr",
                    gap: "0.5rem",
                    fontSize: "0.875rem",
                  }}
                >
                  <div style={{ textAlign: "left" }}>
                    <strong style={{ color: "#374151" }}>Username:</strong>
                    <div
                      style={{
                        color: "#0ea5e9",
                        fontWeight: "600",
                        fontFamily: "monospace",
                      }}
                    >
                      superadmin
                    </div>
                  </div>
                  <div style={{ textAlign: "left" }}>
                    <strong style={{ color: "#374151" }}>Password:</strong>
                    <div
                      style={{
                        color: "#0ea5e9",
                        fontWeight: "600",
                        fontFamily: "monospace",
                      }}
                    >
                      admin123456
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>

        {/* Footer */}
        <div style={{ textAlign: "center", marginTop: "1.5rem" }}>
          <p style={{ fontSize: "0.875rem", color: "#94a3b8" }}>
            Secure • Professional • Reliable
          </p>
        </div>
      </div>

      {/* CSS Animation for spinner */}
      <style>
        {`
          @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }
        `}
      </style>
    </div>
  );
};

export default Login;
