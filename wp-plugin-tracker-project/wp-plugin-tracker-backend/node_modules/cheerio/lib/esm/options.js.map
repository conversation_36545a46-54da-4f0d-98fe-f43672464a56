{"version": 3, "file": "options.js", "sourceRoot": "https://raw.githubusercontent.com/cheeriojs/cheerio/d1cbc66d53392ce8bf6cd0068f675836372d2bf3/src/", "sources": ["options.ts"], "names": [], "mappings": "AAiFA,MAAM,WAAW,GAAmB;IAClC,GAAG,EAAE,KAAK;IACV,cAAc,EAAE,IAAI;CACrB,CAAC;AAEF,+BAA+B;AAC/B,eAAe,WAAW,CAAC;AAE3B,MAAM,cAAc,GAAoB;IACtC,eAAe,EAAE,IAAI;IACrB,OAAO,EAAE,IAAI;CACd,CAAC;AAEF;;;;;;;GAOG;AACH,MAAM,UAAU,OAAO,CACrB,OAA+B;IAE/B,OAAO,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,GAAG;QACjB,CAAC,CAAC,OAAO,OAAO,CAAC,GAAG,KAAK,SAAS;YAChC,CAAC,CAAC,cAAc;YAChB,CAAC,CAAC,EAAE,GAAG,cAAc,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE;QACzC,CAAC,CAAC,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,SAAS,CAAC;AAC3B,CAAC"}