{"version": 3, "file": "index.js", "sourceRoot": "https://raw.githubusercontent.com/cheeriojs/cheerio/d1cbc66d53392ce8bf6cd0068f675836372d2bf3/src/", "sources": ["index.ts"], "names": [], "mappings": "AAOA;;;;GAIG;AACH,cAAc,YAAY,CAAC;AAc3B,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AACpC,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AACtC,OAAO,EAAE,gBAAgB,EAAE,eAAe,EAAE,MAAM,6BAA6B,CAAC;AAChF,OAAO,qBAAqB,MAAM,gBAAgB,CAAC;AACnD,OAAO,EAAE,aAAa,IAAI,oBAAoB,EAAE,MAAM,aAAa,CAAC;AAEpE,MAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,EAAE,CAC/D,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,eAAe;IACxC,CAAC,CAAC,oBAAoB,CAAC,OAAO,EAAE,OAAO,CAAC;IACxC,CAAC,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC,CAC3D,CAAC;AAEF,0EAA0E;AAC1E;;;;;;;;;;;;GAYG;AACH,MAAM,CAAC,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE,CAClD,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,eAAe;IACxC,CAAC,CAAC,qBAAqB,CAAC,GAAG,EAAE,OAAO,CAAC;IACrC,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAC1B,CAAC;AAEF;;;;GAIG;AACH,eAAe,IAAI,CAAC,EAAE,CAAC,CAAC;AAExB,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,aAAa,CAAC;AAE9C,OAAO,KAAK,aAAa,MAAM,aAAa,CAAC;AAE7C;;;;;;;;;;;;;;;;;;GAkBG;AACH,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,GAAG,aAAa,CAAC;AAE1C;;;;;;;;;;;;;GAaG;AACH,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,GAAG,aAAa,CAAC;AAEvC;;;;;;;;;;;;GAYG;AACH,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,GAAG,aAAa,CAAC;AAE3C;;;;;;;;;;;GAWG;AACH,MAAM,CAAC,MAAM,EAAE,IAAI,EAAE,GAAG,aAAa,CAAC"}