{"version": 3, "file": "forms.js", "sourceRoot": "https://raw.githubusercontent.com/cheeriojs/cheerio/d1cbc66d53392ce8bf6cd0068f675836372d2bf3/src/", "sources": ["api/forms.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,KAAK,EAAE,MAAM,aAAa,CAAC;AAEpC;;;GAGG;AACH,MAAM,mBAAmB,GAAG,8BAA8B,CAAC;AAC3D,MAAM,GAAG,GAAG,MAAM,CAAC;AACnB,MAAM,KAAK,GAAG,QAAQ,CAAC;AAEvB;;;;;;;;;;;;;GAaG;AACH,MAAM,UAAU,SAAS;IACvB,gDAAgD;IAChD,MAAM,GAAG,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;IAElC,iDAAiD;IACjD,MAAM,MAAM,GAAG,GAAG,CAAC,GAAG,CACpB,CAAC,IAAI,EAAE,EAAE,CACP,GAAG,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CACvE,CAAC;IAEF,qCAAqC;IACrC,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAC5C,CAAC;AAOD;;;;;;;;;;;;;GAaG;AACH,MAAM,UAAU,cAAc;IAG5B,8EAA8E;IAC9E,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE;QAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC/B,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE;YACvC,OAAO,KAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,OAAO,EAAE,CAAC;SAClD;QACD,OAAO,KAAK,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,OAAO,EAAE,CAAC;IACrD,CAAC,CAAC;SACC,MAAM;IACL,8EAA8E;IAC9E,oBAAoB;QAClB,iGAAiG;QACjG,+CAA+C;QAC/C,sDAAsD;QACtD,8CAA8C;IAChD,+CAA+C;KAChD;SACA,GAAG,CAA2B,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE;;QACzC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC/B,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAW,CAAC,CAAC,oDAAoD;QAC/F,mFAAmF;QACnF,MAAM,KAAK,GAAG,MAAA,KAAK,CAAC,GAAG,EAAE,mCAAI,EAAE,CAAC;QAEhC,+FAA+F;QAC/F,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACxB,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;YACvB;;;eAGG;YACH,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,CAAC,CAC9C,CAAC;SACH;QACD,wEAAwE;QACxE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,CAAC;IACvD,CAAC,CAAC;SACD,OAAO,EAAE,CAAC;AACf,CAAC"}