"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getLoad = void 0;
var options_js_1 = __importStar(require("./options.js"));
var staticMethods = __importStar(require("./static.js"));
var cheerio_js_1 = require("./cheerio.js");
var utils_js_1 = require("./utils.js");
function getLoad(parse, render) {
    /**
     * Create a querying function, bound to a document created from the provided markup.
     *
     * Note that similar to web browser contexts, this operation may introduce
     * `<html>`, `<head>`, and `<body>` elements; set `isDocument` to `false` to
     * switch to fragment mode and disable this.
     *
     * @param content - Markup to be loaded.
     * @param options - Options for the created instance.
     * @param isDocument - Allows parser to be switched to fragment mode.
     * @returns The loaded document.
     * @see {@link https://cheerio.js.org#loading} for additional usage information.
     */
    return function load(content, options, isDocument) {
        if (isDocument === void 0) { isDocument = true; }
        if (content == null) {
            throw new Error('cheerio.load() expects a string');
        }
        var internalOpts = __assign(__assign({}, options_js_1.default), (0, options_js_1.flatten)(options));
        var initialRoot = parse(content, internalOpts, isDocument, null);
        /** Create an extended class here, so that extensions only live on one instance. */
        var LoadedCheerio = /** @class */ (function (_super) {
            __extends(LoadedCheerio, _super);
            function LoadedCheerio() {
                return _super !== null && _super.apply(this, arguments) || this;
            }
            LoadedCheerio.prototype._make = function (selector, context) {
                var cheerio = initialize(selector, context);
                cheerio.prevObject = this;
                return cheerio;
            };
            LoadedCheerio.prototype._parse = function (content, options, isDocument, context) {
                return parse(content, options, isDocument, context);
            };
            LoadedCheerio.prototype._render = function (dom) {
                return render(dom, this.options);
            };
            return LoadedCheerio;
        }(cheerio_js_1.Cheerio));
        function initialize(selector, context, root, opts) {
            if (root === void 0) { root = initialRoot; }
            // $($)
            if (selector && (0, utils_js_1.isCheerio)(selector))
                return selector;
            var options = __assign(__assign({}, internalOpts), (0, options_js_1.flatten)(opts));
            var r = typeof root === 'string'
                ? [parse(root, options, false, null)]
                : 'length' in root
                    ? root
                    : [root];
            var rootInstance = (0, utils_js_1.isCheerio)(r)
                ? r
                : new LoadedCheerio(r, null, options);
            // Add a cyclic reference, so that calling methods on `_root` never fails.
            rootInstance._root = rootInstance;
            // $(), $(null), $(undefined), $(false)
            if (!selector) {
                return new LoadedCheerio(undefined, rootInstance, options);
            }
            var elements = typeof selector === 'string' && (0, utils_js_1.isHtml)(selector)
                ? // $(<html>)
                    parse(selector, options, false, null).children
                : isNode(selector)
                    ? // $(dom)
                        [selector]
                    : Array.isArray(selector)
                        ? // $([dom])
                            selector
                        : undefined;
            var instance = new LoadedCheerio(elements, rootInstance, options);
            if (elements) {
                return instance;
            }
            if (typeof selector !== 'string') {
                throw new Error('Unexpected type of selector');
            }
            // We know that our selector is a string now.
            var search = selector;
            var searchContext = !context
                ? // If we don't have a context, maybe we have a root, from loading
                    rootInstance
                : typeof context === 'string'
                    ? (0, utils_js_1.isHtml)(context)
                        ? // $('li', '<ul>...</ul>')
                            new LoadedCheerio([parse(context, options, false, null)], rootInstance, options)
                        : // $('li', 'ul')
                            ((search = "".concat(context, " ").concat(search)), rootInstance)
                    : (0, utils_js_1.isCheerio)(context)
                        ? // $('li', $)
                            context
                        : // $('li', node), $('li', [nodes])
                            new LoadedCheerio(Array.isArray(context) ? context : [context], rootInstance, options);
            // If we still don't have a context, return
            if (!searchContext)
                return instance;
            /*
             * #id, .class, tag
             */
            return searchContext.find(search);
        }
        // Add in static methods & properties
        Object.assign(initialize, staticMethods, {
            load: load,
            // `_root` and `_options` are used in static methods.
            _root: initialRoot,
            _options: internalOpts,
            // Add `fn` for plugins
            fn: LoadedCheerio.prototype,
            // Add the prototype here to maintain `instanceof` behavior.
            prototype: LoadedCheerio.prototype,
        });
        return initialize;
    };
}
exports.getLoad = getLoad;
function isNode(obj) {
    return (!!obj.name ||
        obj.type === 'root' ||
        obj.type === 'text' ||
        obj.type === 'comment');
}
//# sourceMappingURL=load.js.map