{"version": 3, "file": "parse5-adapter.js", "sourceRoot": "https://raw.githubusercontent.com/cheeriojs/cheerio/d1cbc66d53392ce8bf6cd0068f675836372d2bf3/src/", "sources": ["parsers/parse5-adapter.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,yCAAuE;AACvE,iCAA+E;AAC/E,mFAAgF;AAGhF;;;;;;;;GAQG;AACH,SAAgB,eAAe,CAC7B,OAAe,EACf,OAAwB,EACxB,UAAmB,EACnB,OAA0B;IAE1B,IAAM,IAAI,GAAG;QACX,gBAAgB,EACd,OAAO,OAAO,CAAC,gBAAgB,KAAK,SAAS;YAC3C,CAAC,CAAC,OAAO,CAAC,gBAAgB;YAC1B,CAAC,CAAC,IAAI;QACV,WAAW,EAAE,yCAAkB;QAC/B,sBAAsB,EAAE,OAAO,CAAC,sBAAsB;KACvD,CAAC;IAEF,OAAO,UAAU;QACf,CAAC,CAAC,IAAA,cAAa,EAAC,OAAO,EAAE,IAAI,CAAC;QAC9B,CAAC,CAAC,IAAA,sBAAa,EAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AAC5C,CAAC;AAlBD,0CAkBC;AAED,IAAM,UAAU,GAAG,EAAE,WAAW,EAAE,yCAAkB,EAAE,CAAC;AAEvD;;;;;GAKG;AACH,SAAgB,gBAAgB,CAAC,GAAiC;;IAChE;;;;OAIG;IACH,IAAM,KAAK,GAAG,QAAQ,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAC5C,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,EAAE;QACpD,IAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;QAC1B,IAAI,IAAA,uBAAU,EAAC,IAAI,CAAC,EAAE;YACpB,CAAA,KAAA,KAAK,CAAC,SAAS,CAAC,MAAM,CAAA,CAAC,IAAI,0BAAC,KAAK,EAAE,KAAK,EAAE,CAAC,GAAK,IAAI,CAAC,QAAQ,UAAE;SAChE;KACF;IAED,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,EAAE;QACpD,IAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;QAC1B,MAAM,IAAI,IAAA,uBAAc,EAAC,IAAI,EAAE,UAAU,CAAC,CAAC;KAC5C;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AArBD,4CAqBC"}