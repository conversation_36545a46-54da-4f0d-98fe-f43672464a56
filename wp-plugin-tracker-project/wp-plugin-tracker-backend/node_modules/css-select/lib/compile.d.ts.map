{"version": 3, "file": "compile.d.ts", "sourceRoot": "https://raw.githubusercontent.com/fb55/css-select/0f0725a9dfeddd2fdb54eda9656cdbab5bbf6be6/src/", "sources": ["compile.ts"], "names": [], "mappings": "AAAA,OAAO,EAAS,QAAQ,EAAgB,MAAM,UAAU,CAAC;AAQzD,OAAO,KAAK,EACR,aAAa,EACb,eAAe,EACf,gBAAgB,EACnB,MAAM,YAAY,CAAC;AAEpB;;;;;;GAMG;AACH,wBAAgB,OAAO,CAAC,IAAI,EAAE,WAAW,SAAS,IAAI,EAClD,QAAQ,EAAE,MAAM,GAAG,QAAQ,EAAE,EAAE,EAC/B,OAAO,EAAE,eAAe,CAAC,IAAI,EAAE,WAAW,CAAC,EAC3C,OAAO,CAAC,EAAE,IAAI,EAAE,GAAG,IAAI,GACxB,aAAa,CAAC,IAAI,CAAC,CAGrB;AAED,wBAAgB,aAAa,CAAC,IAAI,EAAE,WAAW,SAAS,IAAI,EACxD,QAAQ,EAAE,MAAM,GAAG,QAAQ,EAAE,EAAE,EAC/B,OAAO,EAAE,eAAe,CAAC,IAAI,EAAE,WAAW,CAAC,EAC3C,OAAO,CAAC,EAAE,IAAI,EAAE,GAAG,IAAI,GACxB,aAAa,CAAC,WAAW,CAAC,CAG5B;AAqDD,wBAAgB,YAAY,CAAC,IAAI,EAAE,WAAW,SAAS,IAAI,EACvD,KAAK,EAAE,gBAAgB,EAAE,EAAE,EAC3B,OAAO,EAAE,eAAe,CAAC,IAAI,EAAE,WAAW,CAAC,EAC3C,OAAO,CAAC,EAAE,IAAI,EAAE,GAAG,IAAI,GACxB,aAAa,CAAC,WAAW,CAAC,CAsD5B"}