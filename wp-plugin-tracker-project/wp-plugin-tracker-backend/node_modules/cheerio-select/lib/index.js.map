{"version": 3, "file": "index.js", "sourceRoot": "https://raw.githubusercontent.com/cheeriojs/cheerio-select/ef063a6ca4c3f0d02d2fc3505e750b6fb81c448d/src/", "sources": ["index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,qCAA2E;AAC3E,yCAIoB;AACpB,iDAAqC;AACrC,iDAAqC;AAErC,2CAA+D;AAC/D,mDAK0B;AAE1B,oCAAoC;AACpC,yCAAuD;AAA9C,qGAAA,OAAO,OAAA;AAAE,qGAAA,OAAO,OAAA;AAAE,qGAAA,OAAO,OAAA;AAElC,IAAM,kBAAkB,GAAa;IACjC,IAAI,EAAE,uBAAY,CAAC,SAAS;IAC5B,SAAS,EAAE,IAAI;CAClB,CAAC;AACF,IAAM,YAAY,GAAa;IAC3B,IAAI,EAAE,uBAAY,CAAC,MAAM;IACzB,IAAI,EAAE,OAAO;IACb,IAAI,EAAE,IAAI;CACb,CAAC;AAOF,SAAgB,EAAE,CACd,OAAgB,EAChB,QAA6C,EAC7C,OAAqB;IAArB,wBAAA,EAAA,YAAqB;IAErB,OAAO,IAAI,CAAC,CAAC,OAAO,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;AAC9C,CAAC;AAND,gBAMC;AAED,SAAgB,IAAI,CAChB,QAAmB,EACnB,QAA6C,EAC7C,OAAqB;IAArB,wBAAA,EAAA,YAAqB;IAErB,IAAI,OAAO,QAAQ,KAAK,UAAU;QAAE,OAAO,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAE7D,IAAA,KAAoB,IAAA,2BAAc,EAAC,IAAA,gBAAK,EAAC,QAAQ,CAAC,CAAC,EAAlD,KAAK,QAAA,EAAE,QAAQ,QAAmC,CAAC;IAE1D,OAAO,CACH,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAA,0BAAY,EAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;QACjE,QAAQ,CAAC,IAAI,CACT,UAAC,GAAG,IAAK,OAAA,gBAAgB,CAAC,GAAG,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,EAAnD,CAAmD,CAC/D,CACJ,CAAC;AACN,CAAC;AAfD,oBAeC;AAED,SAAS,gBAAgB,CACrB,MAAc,EACd,KAAgB,EAChB,IAAkC,EAClC,OAAgB;IAEhB,IAAM,GAAG,GAAG,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;IAEhE,QAAQ,MAAM,EAAE;QACZ,KAAK,OAAO,CAAC;QACb,KAAK,IAAI;YACL,6BAA6B;YAC7B,OAAO,KAAK,CAAC;QACjB,KAAK,MAAM;YACP,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAChE,KAAK,KAAK,CAAC;QACX,KAAK,IAAI;YACL,OAAO,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM;gBAChD,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACpD,CAAC,CAAC,EAAE,CAAC;QACb,KAAK,IAAI;YACL,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACrD,KAAK,MAAM;YACP,OAAO,KAAK,CAAC,MAAM,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,GAAG,CAAC,KAAK,CAAC,EAAX,CAAW,CAAC,CAAC;QAC/C,KAAK,KAAK;YACN,OAAO,KAAK,CAAC,MAAM,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,GAAG,CAAC,KAAK,CAAC,EAAX,CAAW,CAAC,CAAC;QAC/C,KAAK,KAAK,CAAC,CAAC;YACR,IAAM,UAAQ,GAAG,IAAI,GAAG,CACpB,YAAY,CAAC,IAAoB,EAAE,KAAK,EAAE,OAAO,CAAC,CACrD,CAAC;YAEF,OAAO,KAAK,CAAC,MAAM,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,UAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAhB,CAAgB,CAAC,CAAC;SAChD;KACJ;AACL,CAAC;AAED,SAAgB,MAAM,CAClB,QAAgB,EAChB,QAAmB,EACnB,OAAqB;IAArB,wBAAA,EAAA,YAAqB;IAErB,OAAO,YAAY,CAAC,IAAA,gBAAK,EAAC,QAAQ,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;AAC5D,CAAC;AAND,wBAMC;AAED;;;;;;;;GAQG;AACH,SAAS,YAAY,CACjB,QAAsB,EACtB,QAAmB,EACnB,OAAgB;IAEhB,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,EAAE,CAAC;IAE/B,IAAA,KAAsC,IAAA,2BAAc,EAAC,QAAQ,CAAC,EAA7D,cAAc,QAAA,EAAE,iBAAiB,QAA4B,CAAC;IACrE,IAAI,KAA+B,CAAC;IAEpC,IAAI,cAAc,CAAC,MAAM,EAAE;QACvB,IAAM,QAAQ,GAAG,cAAc,CAAC,QAAQ,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;QAEnE,uCAAuC;QACvC,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE;YAChC,OAAO,QAAQ,CAAC;SACnB;QAED,0CAA0C;QAC1C,IAAI,QAAQ,CAAC,MAAM,EAAE;YACjB,KAAK,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC;SAC7B;KACJ;IAED,KACI,IAAI,CAAC,GAAG,CAAC,EACT,CAAC,GAAG,iBAAiB,CAAC,MAAM,IAAI,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,IAAI,MAAK,QAAQ,CAAC,MAAM,EAC/D,CAAC,EAAE,EACL;QACE,IAAM,gBAAgB,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC;QAC9C,IAAM,OAAO,GAAG,KAAK;YACjB,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAC,CAAC,IAAK,OAAA,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAnC,CAAmC,CAAC;YAC7D,CAAC,CAAC,QAAQ,CAAC;QAEf,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;YAAE,MAAM;QAChC,IAAM,QAAQ,GAAG,gBAAgB,CAAC,gBAAgB,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;QAEvE,IAAI,QAAQ,CAAC,MAAM,EAAE;YACjB,IAAI,CAAC,KAAK,EAAE;gBACR;;;mBAGG;gBACH,IAAI,CAAC,KAAK,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;oBACpC,OAAO,QAAQ,CAAC;iBACnB;gBAED,KAAK,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC;aAC7B;iBAAM;gBACH,QAAQ,CAAC,OAAO,CAAC,UAAC,EAAE,IAAK,OAAA,KAAM,CAAC,GAAG,CAAC,EAAE,CAAC,EAAd,CAAc,CAAC,CAAC;aAC5C;SACJ;KACJ;IAED,OAAO,OAAO,KAAK,KAAK,WAAW;QAC/B,CAAC,CAAE,CAAC,KAAK,CAAC,IAAI,KAAK,QAAQ,CAAC,MAAM;YAC5B,CAAC,CAAC,QAAQ;YACV,CAAC,CAAC,oCAAoC;gBACpC,QAAQ,CAAC,MAAM,CAAC,UAAC,EAAE;oBACf,OAAC,KAAsB,CAAC,GAAG,CAAC,EAAE,CAAC;gBAA/B,CAA+B,CAClC,CAAe;QACxB,CAAC,CAAC,EAAE,CAAC;AACb,CAAC;AAED,SAAS,gBAAgB,CACrB,QAAoB,EACpB,QAAmB,EACnB,OAAgB;;IAEhB,IAAI,QAAQ,CAAC,IAAI,CAAC,sBAAW,CAAC,EAAE;QAC5B;;;WAGG;QACH,IAAM,IAAI,GAAG,MAAA,OAAO,CAAC,IAAI,mCAAI,IAAA,4BAAe,EAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,IAAM,IAAI,yBAAQ,OAAO,KAAE,OAAO,EAAE,QAAQ,EAAE,gBAAgB,EAAE,KAAK,GAAE,CAAC;QACxE,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC5B,OAAO,kBAAkB,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;KAC1E;IACD,2EAA2E;IAC3E,OAAO,kBAAkB,CACrB,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,KAAK,EACL,QAAQ,CAAC,MAAM,CAClB,CAAC;AACN,CAAC;AAED,SAAgB,MAAM,CAClB,QAA6C,EAC7C,IAAyB,EACzB,OAAqB,EACrB,KAAgB;IADhB,wBAAA,EAAA,YAAqB;IACrB,sBAAA,EAAA,gBAAgB;IAEhB,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;QAChC,OAAO,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;KAC/B;IAEK,IAAA,KAAoB,IAAA,2BAAc,EAAC,IAAA,gBAAK,EAAC,QAAQ,CAAC,CAAC,EAAlD,KAAK,QAAA,EAAE,QAAQ,QAAmC,CAAC;IAE1D,IAAM,OAAO,GAAgB,QAAQ,CAAC,GAAG,CAAC,UAAC,GAAG;QAC1C,OAAA,kBAAkB,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC;IAAnD,CAAmD,CACtD,CAAC;IAEF,gDAAgD;IAChD,IAAI,KAAK,CAAC,MAAM,EAAE;QACd,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;KAC3D;IAED,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;QACtB,OAAO,EAAE,CAAC;KACb;IAED,8DAA8D;IAC9D,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;QACtB,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC;KACrB;IAED,yCAAyC;IACzC,OAAO,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,uCAAI,CAAC,SAAK,CAAC,SAAX,CAAY,CAAC,CAAC,CAAC;AACvE,CAAC;AAhCD,wBAgCC;AAED;;;;;;GAMG;AACH,SAAS,kBAAkB,CACvB,IAAyB,EACzB,QAAoB,EACpB,OAAgB,EAChB,gBAAyB,EACzB,UAAkB;IAElB,IAAM,WAAW,GAAG,QAAQ,CAAC,SAAS,CAAC,yBAAQ,CAAC,CAAC;IACjD,IAAM,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;IAC3C,IAAM,MAAM,GAAG,QAAQ,CAAC,WAAW,CAAoB,CAAC;IACxD,yFAAyF;IACzF,IAAM,SAAS,GACX,QAAQ,CAAC,MAAM,GAAG,CAAC,KAAK,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC;IAEhE;;;OAGG;IACH,IAAM,KAAK,GAAG,IAAA,yBAAQ,EAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAE5D,IAAI,KAAK,KAAK,CAAC;QAAE,OAAO,EAAE,CAAC;IAE3B;;;OAGG;IACH,IAAM,YAAY,GACd,GAAG,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;QACpC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC;QACnD,CAAC,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC;YAClB,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC;YAC9D,CAAC,CAAC,gBAAgB,IAAI,GAAG,CAAC,IAAI,CAAC,sBAAW,CAAC;gBAC3C,CAAC,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC;gBAC3C,CAAC,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC;IAE/C,IAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IAE3C,IAAI,MAAM,GAAG,gBAAgB,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAExE,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,QAAQ,CAAC,MAAM,KAAK,WAAW,GAAG,CAAC,EAAE;QAC5D,OAAO,MAAM,CAAC;KACjB;IAED,IAAM,iBAAiB,GAAG,QAAQ,CAAC,KAAK,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;IAC1D,IAAM,qBAAqB,GAAG,iBAAiB,CAAC,IAAI,CAAC,sBAAW,CAAC,CAAC;IAElE,IAAI,qBAAqB,EAAE;QACvB,IAAI,IAAA,sBAAW,EAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,EAAE;YAC3B,IAAA,IAAI,GAAK,iBAAiB,CAAC,CAAC,CAAC,KAAzB,CAA0B;YAEtC,IACI,IAAI,KAAK,uBAAY,CAAC,OAAO;gBAC7B,IAAI,KAAK,uBAAY,CAAC,QAAQ,EAChC;gBACE,wEAAwE;gBACxE,MAAM,GAAG,IAAA,2BAAc,EAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAc,CAAC;aAChE;YAED,0CAA0C;YAC1C,iBAAiB,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;SACjD;QAED,OAAO,yBACA,OAAO;YACV,kCAAkC;YAClC,gBAAgB,EAAE,KAAK;YACvB;;;eAGG;YACH,QAAQ,EAAE,UAAC,EAAW,IAAK,OAAA,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAnB,CAAmB,GACjD,CAAC;KACL;SAAM,IAAI,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,CAAC,QAAQ,EAAE;QACnE,OAAO,yBAAQ,OAAO,KAAE,QAAQ,EAAE,QAAQ,CAAC,QAAQ,GAAE,CAAC;KACzD;IAED;;;;;;OAMG;IACH,OAAO,iBAAiB,CAAC,IAAI,CAAC,yBAAQ,CAAC;QACnC,CAAC,CAAC,kBAAkB,CACd,MAAM,EACN,iBAAiB,EACjB,OAAO,EACP,KAAK,EACL,UAAU,CACb;QACH,CAAC,CAAC,qBAAqB;YACvB,CAAC,CAAC,gDAAgD;gBAChD,YAAY,CAAC,MAAM,EAAE,CAAC,iBAAiB,CAAC,EAAE,OAAO,EAAE,UAAU,CAAC;YAChE,CAAC,CAAC,gEAAgE;gBAChE,cAAc,CAAC,MAAM,EAAE,CAAC,iBAAiB,CAAC,EAAE,OAAO,CAAC,CAAC;AAC/D,CAAC;AAOD,SAAS,YAAY,CACjB,IAAyB,EACzB,GAAiB,EACjB,OAAgB,EAChB,KAAa;IAEb,IAAM,KAAK,GAAkB,IAAA,0BAAY,EACrC,GAAG,EACH,OAAO,EACP,IAAI,CACP,CAAC;IAEF,OAAO,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AACpC,CAAC;AAED,SAAS,IAAI,CACT,IAAyB,EACzB,KAAoB,EACpB,KAAgB;IAAhB,sBAAA,EAAA,gBAAgB;IAEhB,IAAM,KAAK,GAAG,IAAA,2BAAc,EACxB,IAAI,EACJ,QAAQ,EACR,KAAK,CAAC,sBAAsB,CAC/B,CAAC;IAEF,OAAO,QAAQ,CAAC,IAAI,CAChB,UAAC,IAAa,IAAK,OAAA,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,EAAnC,CAAmC,EACtD,KAAK,EACL,IAAI,EACJ,KAAK,CACK,CAAC;AACnB,CAAC;AAED,SAAS,cAAc,CACnB,QAA6B,EAC7B,GAAiB,EACjB,OAAgB;IAEhB,IAAM,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAChE,QAAQ,CAAC,KAAK,CACjB,CAAC;IAEF,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,GAAG,CAAC;IAEjC,IAAM,KAAK,GAAG,IAAA,0BAAY,EAAmB,GAAG,EAAE,OAAO,CAAC,CAAC;IAC3D,OAAO,KAAK,KAAK,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACjE,CAAC"}