# cheerio-select [![NPM version](http://img.shields.io/npm/v/cheerio-select.svg)](https://npmjs.org/package/cheerio-select) [![Build Status](https://travis-ci.org/cheeriojs/cheerio-select.svg?branch=master)](http://travis-ci.org/cheeriojs/cheerio-select) [![Downloads](https://img.shields.io/npm/dm/cheerio-select.svg)](https://npmjs.org/package/cheerio-select) [![Coverage](https://coveralls.io/repos/cheeriojs/cheerio-select/badge.svg?branch=master)](https://coveralls.io/r/cheeriojs/cheerio-select)

CSS selector engine supporting jQuery selectors, based on [`css-select`](https://github.com/fb55/css-select).

Supports all jQuery positional pseudo-selectors:

-   `:first`
-   `:last`
-   `:eq`
-   `:nth`
-   `:gt`
-   `:lt`
-   `:even`
-   `:odd`
-   `:not(:positional)`, where `:positional` is any of the above.

This library is a thin wrapper around [`css-select`](https://github.com/fb55/css-select).
Only use this module if you will actually use jQuery positional selectors.
