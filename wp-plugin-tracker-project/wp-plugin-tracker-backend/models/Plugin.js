const mongoose = require("mongoose");

// Import DownloadHistory model (will be defined after this model)
let DownloadHistory;

const supportTicketSchema = new mongoose.Schema(
  {
    totalTopics: {
      type: Number,
      default: 0,
    },
    unresolvedTopics: {
      type: Number,
      default: 0,
    },
    lastActivity: {
      type: Date,
    },
  },
  { _id: false }
);

const ratingBreakdownSchema = new mongoose.Schema(
  {
    five: { type: Number, default: 0 },
    four: { type: Number, default: 0 },
    three: { type: Number, default: 0 },
    two: { type: Number, default: 0 },
    one: { type: Number, default: 0 },
  },
  { _id: false }
);

const pluginSchema = new mongoose.Schema(
  {
    slug: {
      type: String,
      required: [true, "Plugin slug is required"],
      unique: true,
      trim: true,
      lowercase: true,
      match: [
        /^[a-z0-9-]+$/,
        "Plugin slug can only contain lowercase letters, numbers, and hyphens",
      ],
    },
    name: {
      type: String,
      required: [true, "Plugin name is required"],
      trim: true,
      maxlength: [200, "Plugin name cannot be more than 200 characters"],
    },
    description: {
      type: String,
      trim: true,
      maxlength: [1000, "Description cannot be more than 1000 characters"],
    },
    shortDescription: {
      type: String,
      trim: true,
      maxlength: [500, "Short description cannot be more than 500 characters"],
    },
    version: {
      type: String,
      default: "",
    },
    author: {
      type: String,
      default: "",
    },
    authorProfile: {
      type: String,
      default: "",
    },
    homepage: {
      type: String,
      default: "",
    },
    downloadLink: {
      type: String,
      default: "",
    },
    downloads: {
      type: Number,
      default: 0,
    },
    activeInstalls: {
      type: Number,
      default: 0,
    },
    pluginRank: {
      type: Number,
      default: 0,
    },
    rating: {
      type: Number,
      default: 0,
      min: 0,
      max: 5,
    },
    ratingCount: {
      type: Number,
      default: 0,
    },
    ratingBreakdown: ratingBreakdownSchema,
    tags: [
      {
        type: String,
        trim: true,
      },
    ],
    status: {
      type: String,
      enum: ["active", "closed", "disabled", "unknown"],
      default: "unknown",
    },
    wpVersion: {
      type: String,
      default: "",
    },
    testedUpTo: {
      type: String,
      default: "",
    },
    requiresPhp: {
      type: String,
      default: "",
    },
    lastUpdated: {
      type: Date,
    },
    lastReleaseDate: {
      type: Date,
    },
    daysSinceLastRelease: {
      type: Number,
      default: 0,
    },
    added: {
      type: Date,
    },
    supportTickets: supportTicketSchema,
    screenshots: [
      {
        src: String,
        caption: String,
      },
    ],
    banners: {
      low: String,
      high: String,
    },
    icons: {
      "1x": String,
      "2x": String,
      svg: String,
    },
    contributors: [
      {
        type: String,
      },
    ],
    donateLink: {
      type: String,
      default: "",
    },
    // Tracking metadata
    trackingEnabled: {
      type: Boolean,
      default: true,
    },
    lastDataFetch: {
      type: Date,
      default: Date.now,
    },
    fetchErrors: [
      {
        error: String,
        timestamp: {
          type: Date,
          default: Date.now,
        },
      },
    ],
    addedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    notes: {
      type: String,
      maxlength: [500, "Notes cannot be more than 500 characters"],
    },
  },
  {
    timestamps: true,
  }
);

// Indexes for better query performance (slug already indexed via unique: true)
pluginSchema.index({ name: "text", description: "text" });
pluginSchema.index({ downloads: -1 });
pluginSchema.index({ rating: -1 });
pluginSchema.index({ lastUpdated: -1 });
pluginSchema.index({ addedBy: 1 });

// Virtual for WordPress.org URL
pluginSchema.virtual("wpUrl").get(function () {
  return `https://wordpress.org/plugins/${this.slug}/`;
});

// Virtual for support URL
pluginSchema.virtual("supportUrl").get(function () {
  return `https://wordpress.org/support/plugin/${this.slug}/`;
});

// Virtual for reviews URL
pluginSchema.virtual("reviewsUrl").get(function () {
  return `https://wordpress.org/support/plugin/${this.slug}/reviews/`;
});

// Method to calculate total support topics
pluginSchema.methods.getTotalSupportTopics = function () {
  return this.supportTickets?.totalTopics || 0;
};

// Method to get rating percentage
pluginSchema.methods.getRatingPercentage = function () {
  if (this.ratingCount === 0) return 0;
  return Math.round((this.rating / 5) * 100);
};

// Static method to get plugin statistics
pluginSchema.statics.getStats = async function () {
  const stats = await this.aggregate([
    {
      $group: {
        _id: null,
        totalPlugins: { $sum: 1 },
        totalDownloads: { $sum: "$downloads" },
        averageRating: { $avg: "$rating" },
        totalTopics: {
          $sum: { $ifNull: ["$supportTickets.totalTopics", 0] },
        },
        totalUnresolvedTopics: {
          $sum: { $ifNull: ["$supportTickets.unresolvedTopics", 0] },
        },
      },
    },
  ]);

  return (
    stats[0] || {
      totalPlugins: 0,
      totalDownloads: 0,
      averageRating: 0,
      totalTopics: 0,
      totalUnresolvedTopics: 0,
    }
  );
};

// Post-save middleware to record daily stats
pluginSchema.post("save", async function (doc) {
  try {
    // Lazy load DownloadHistory to avoid circular dependency
    if (!DownloadHistory) {
      DownloadHistory = require("./DownloadHistory");
    }

    // Record daily stats when plugin data is updated
    await DownloadHistory.recordDailyStats(doc);
  } catch (error) {
    console.error("Error recording daily stats in post-save:", error);
  }
});

module.exports = mongoose.model("Plugin", pluginSchema);
