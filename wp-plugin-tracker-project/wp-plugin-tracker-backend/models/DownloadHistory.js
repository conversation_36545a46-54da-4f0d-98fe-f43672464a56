const mongoose = require("mongoose");

const downloadHistorySchema = new mongoose.Schema(
  {
    pluginId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Plugin",
      required: true,
    },
    slug: {
      type: String,
      required: true,
      index: true,
    },
    date: {
      type: Date,
      required: true,
      index: true,
    },
    downloads: {
      type: Number,
      required: true,
      default: 0,
    },
    activeInstalls: {
      type: Number,
      default: 0,
    },
    rating: {
      type: Number,
      default: 0,
    },
    pluginRank: {
      type: Number,
      default: 0,
    },
    unresolvedTopics: {
      type: Number,
      default: 0,
    },
  },
  {
    timestamps: true,
  }
);

// Compound index for efficient queries
downloadHistorySchema.index({ slug: 1, date: -1 });
downloadHistorySchema.index({ pluginId: 1, date: -1 });

// Static method to record daily stats
downloadHistorySchema.statics.recordDailyStats = async function (pluginData) {
  const today = new Date();
  today.setHours(0, 0, 0, 0); // Start of day

  try {
    // Check if record already exists for today
    const existingRecord = await this.findOne({
      pluginId: pluginData._id,
      date: today,
    });

    const historyData = {
      pluginId: pluginData._id,
      slug: pluginData.slug,
      date: today,
      downloads: pluginData.downloads || 0,
      activeInstalls: pluginData.activeInstalls || 0,
      rating: pluginData.rating || 0,
      pluginRank: pluginData.pluginRank || 0,
      unresolvedTopics: pluginData.supportTickets?.unresolvedTopics || 0,
    };

    if (existingRecord) {
      // Update existing record
      await this.findByIdAndUpdate(existingRecord._id, historyData);
    } else {
      // Create new record
      await this.create(historyData);
    }
  } catch (error) {
    console.error("Error recording daily stats:", error);
  }
};

// Static method to get download history for a plugin
downloadHistorySchema.statics.getPluginHistory = async function (
  pluginId,
  days = 7
) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  startDate.setHours(0, 0, 0, 0);

  return await this.find({
    pluginId: pluginId,
    date: { $gte: startDate },
  })
    .sort({ date: -1 })
    .lean();
};

// Static method to get recent download changes
downloadHistorySchema.statics.getRecentChanges = async function (days = 2) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  startDate.setHours(0, 0, 0, 0);

  return await this.aggregate([
    {
      $match: {
        date: { $gte: startDate },
      },
    },
    {
      $sort: { slug: 1, date: -1 },
    },
    {
      $group: {
        _id: "$slug",
        pluginId: { $first: "$pluginId" },
        history: {
          $push: {
            date: "$date",
            downloads: "$downloads",
            activeInstalls: "$activeInstalls",
            rating: "$rating",
            pluginRank: "$pluginRank",
            unresolvedTopics: "$unresolvedTopics",
          },
        },
      },
    },
    {
      $lookup: {
        from: "plugins",
        localField: "pluginId",
        foreignField: "_id",
        as: "plugin",
      },
    },
    {
      $unwind: "$plugin",
    },
    {
      $project: {
        slug: "$_id",
        pluginName: "$plugin.name",
        history: 1,
        currentDownloads: "$plugin.downloads",
        currentRank: "$plugin.pluginRank",
        currentUnresolvedTopics: "$plugin.supportTickets.unresolvedTopics",
      },
    },
  ]);
};

module.exports = mongoose.model("DownloadHistory", downloadHistorySchema);
