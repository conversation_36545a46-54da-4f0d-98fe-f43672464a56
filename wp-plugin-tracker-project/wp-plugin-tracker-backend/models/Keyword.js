const mongoose = require("mongoose");

const keywordSchema = new mongoose.Schema(
  {
    keyword: {
      type: String,
      required: [true, "Keyword is required"],
      trim: true,
      maxlength: [200, "Keyword cannot be more than 200 characters"],
    },
    pluginId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Plugin",
      required: [true, "Plugin ID is required"],
    },
    pluginSlug: {
      type: String,
      required: [true, "Plugin slug is required"],
      trim: true,
    },
    pluginName: {
      type: String,
      required: [true, "Plugin name is required"],
      trim: true,
    },
    // Ranking data
    currentRank: {
      type: Number,
      default: null,
    },
    previousRank: {
      type: Number,
      default: null,
    },
    rankChange: {
      type: Number,
      default: 0,
    },
    // Readme analysis data
    occurrences: {
      type: Number,
      default: 0,
    },
    readmeLength: {
      type: Number,
      default: 0,
    },
    contexts: [
      {
        lineNumber: Number,
        content: String,
      },
    ],
    // Tracking dates
    tracked: {
      type: Date,
      default: Date.now,
      required: true,
    },
    updated: {
      type: Date,
      default: Date.now,
    },
    // Additional metadata
    url: {
      type: String,
      default: "",
    },
    notes: {
      type: String,
      maxlength: [500, "Notes cannot be more than 500 characters"],
    },
    addedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

// Indexes for better performance
keywordSchema.index({ pluginId: 1, keyword: 1 }, { unique: true });
keywordSchema.index({ keyword: 1 });
keywordSchema.index({ pluginSlug: 1 });
keywordSchema.index({ currentRank: 1 });
keywordSchema.index({ tracked: 1 });
keywordSchema.index({ updated: 1 });

// Virtual for rank change direction
keywordSchema.virtual("rankDirection").get(function () {
  if (this.rankChange > 0) return "up";
  if (this.rankChange < 0) return "down";
  return "same";
});

// Virtual for rank change display
keywordSchema.virtual("rankChangeDisplay").get(function () {
  if (this.rankChange === 0) return "No change";
  if (this.rankChange > 0) return `+${this.rankChange}`;
  return `${this.rankChange}`;
});

// Method to update ranking data
keywordSchema.methods.updateRanking = function (rankData) {
  this.previousRank = this.currentRank;
  this.currentRank = rankData.currentRank;
  this.rankChange = this.previousRank && this.currentRank 
    ? this.previousRank - this.currentRank 
    : 0;
  this.updated = new Date();
  return this.save();
};

// Method to update readme analysis data
keywordSchema.methods.updateReadmeAnalysis = function (analysisData) {
  this.occurrences = analysisData.occurrences;
  this.readmeLength = analysisData.readmeLength;
  this.contexts = analysisData.contexts || [];
  this.updated = new Date();
  return this.save();
};

// Static method to find keywords by plugin
keywordSchema.statics.findByPlugin = function (pluginId) {
  return this.find({ pluginId }).populate("addedBy", "name username");
};

// Static method to find keywords by plugin slug
keywordSchema.statics.findByPluginSlug = function (pluginSlug) {
  return this.find({ pluginSlug }).populate("addedBy", "name username");
};

// Pre-save middleware to update the 'updated' field
keywordSchema.pre("save", function (next) {
  if (this.isModified() && !this.isNew) {
    this.updated = new Date();
  }
  next();
});

module.exports = mongoose.model("Keyword", keywordSchema);
