const jwt = require('jsonwebtoken')
const User = require('../models/User')

// Protect routes - verify JWT token
const protect = async (req, res, next) => {
  try {
    let token

    // Check for token in Authorization header
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1]
    }

    // Make sure token exists
    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Not authorized to access this route'
      })
    }

    try {
      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET)

      // Get user from token
      const user = await User.findById(decoded.id)

      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'No user found with this token'
        })
      }

      // Check if user is active
      if (user.status !== 'active') {
        return res.status(401).json({
          success: false,
          message: 'User account is inactive'
        })
      }

      req.user = user
      next()
    } catch (error) {
      return res.status(401).json({
        success: false,
        message: 'Not authorized to access this route'
      })
    }
  } catch (error) {
    next(error)
  }
}

// Grant access to specific roles
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: `User role ${req.user.role} is not authorized to access this route`
      })
    }
    next()
  }
}

// Check if user is admin or superadmin
const isAdmin = (req, res, next) => {
  if (!['admin', 'superadmin'].includes(req.user.role)) {
    return res.status(403).json({
      success: false,
      message: 'Admin access required'
    })
  }
  next()
}

// Check if user is superadmin
const isSuperAdmin = (req, res, next) => {
  if (req.user.role !== 'superadmin') {
    return res.status(403).json({
      success: false,
      message: 'Super admin access required'
    })
  }
  next()
}

// Check if user can modify the target user
const canModifyUser = async (req, res, next) => {
  try {
    const targetUserId = req.params.id
    const currentUser = req.user

    // Super admin can modify anyone
    if (currentUser.role === 'superadmin') {
      return next()
    }

    // Admin can modify agents and themselves
    if (currentUser.role === 'admin') {
      const targetUser = await User.findById(targetUserId)
      
      if (!targetUser) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        })
      }

      // Admin can modify agents or themselves
      if (targetUser.role === 'agent' || targetUser._id.toString() === currentUser._id.toString()) {
        return next()
      }

      return res.status(403).json({
        success: false,
        message: 'Not authorized to modify this user'
      })
    }

    // Agents can only modify themselves
    if (currentUser._id.toString() === targetUserId) {
      return next()
    }

    return res.status(403).json({
      success: false,
      message: 'Not authorized to modify this user'
    })
  } catch (error) {
    next(error)
  }
}

module.exports = {
  protect,
  authorize,
  isAdmin,
  isSuperAdmin,
  canModifyUser
}
